package com.sinitek.sirm.nocode.page.dto;

import com.sinitek.sirm.framework.frontend.dto.UploadDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @version 2025.0312
 * @since 1.0.0-SNAPSHOT
 */
@ApiModel(description = "页面发布DTO")
@Data
public class ZdPagePublishDTO {
    /**
     * 自定义的页面地址
     */
    @NotBlank(message = "自定义的页面地址不能为空")
    @ApiModelProperty(value = "自定义的页面地址", example = "jicode123456", required = true)
    private String url;

    /**
     * 页面编码
     */
    @NotBlank(message = "表单编不能为空")
    @ApiModelProperty(value = "表单编码", example = "page_57049cf9c4e94acfbc94d6775bc9e73e", required = true)
    private String code;

    /**
     * 是否发布到广场
     */
    @ApiModelProperty(value = "是否发布到广场", example = "true")
    private Boolean publishToSquare;

    /**
     * 发布页面文件
     */
    @ApiModelProperty(value = "发布页面文件")
    private UploadDTO uploadDTO;


}
