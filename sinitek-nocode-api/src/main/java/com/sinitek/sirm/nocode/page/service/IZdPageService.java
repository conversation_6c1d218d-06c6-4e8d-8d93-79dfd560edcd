package com.sinitek.sirm.nocode.page.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sinitek.sirm.common.component.dto.XnSelectDTO;
import com.sinitek.sirm.nocode.common.dto.ZdOptionDTO;
import com.sinitek.sirm.nocode.page.dto.ZdPageCodeAndAppCodeDTO;
import com.sinitek.sirm.nocode.page.dto.ZdPageDTO;
import com.sinitek.sirm.nocode.page.dto.ZdPagePublishDTO;
import com.sinitek.sirm.nocode.page.dto.ZdPageSaveResultDTO;
import com.sinitek.sirm.nocode.page.dto.ZdPageSquareDTO;
import com.sinitek.sirm.nocode.page.dto.ZdPageSquareSearchParamDTO;
import com.sinitek.sirm.nocode.page.dto.ZdPageUpdateNameDTO;
import com.sinitek.sirm.nocode.page.enumerate.CustomUrlTypeEnum;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2025-03-12 10:00:09
 * @description 针对表【zd_page(页面表)】的数据库操作Service
 */
public interface IZdPageService {
    /**
     * 更新页面名称
     *
     * @param zdPageUpdateNameDTO 页面名称
     * @return 是否成功
     */
    boolean updateName(ZdPageUpdateNameDTO zdPageUpdateNameDTO);

    /**
     * 获取页面名称
     *
     * @param code code
     * @return 名称
     */
    String getNameByCode(String code);


    /**
     * 获取页面自定义URL配置
     *
     * @param code 页面编码
     * @return 自定义URL配置信息
     * @implNote 返回的ZdPageCustomUrlDTO包含完整URL配置数据
     */
    ZdPagePublishDTO getUrlByCode(String code);


    /**
     * 根据自定义URL和类型获取页面关联信息
     *
     * @param url  自定义访问地址
     * @param type 自定义URL类型（见CustomUrlTypeEnum）
     * @return 包含页面编码和应用编码的关联信息
     * @implNote 支持PC端和移动端两种URL类型的查询
     */
    ZdPageCodeAndAppCodeDTO getByUrl(String url, CustomUrlTypeEnum type);


    /**
     * 保存页面
     *
     * @param pageDTO 页面参数
     * @return pageCode
     */
    ZdPageSaveResultDTO savePage(ZdPageDTO pageDTO);

    /**
     * 通过页面编码删除页面
     *
     * @param code 页面编码
     * @return 是否成功
     */
    Boolean deleteByCode(String code);

    /**
     * 通过页面编码批量删除页面
     *
     * @param codeList 页面编码
     * @return 是否成功
     */
    Boolean deleteByCode(List<String> codeList);

    /**
     * 判断页面是否存在
     *
     * @param code 页面编码
     * @return 是否存在
     */
    boolean exists(String code);

    /**
     * 获取页面列表
     *
     * @param appCode 应用编码
     * @return 页面列表
     */

    List<ZdPageDTO> listTree(String appCode, Integer type);

    /**
     * 获取已经发布的页面列表
     *
     * @param appCode 应用编码
     * @return 页面列表
     */
    List<ZdPageDTO> publishedListTree(String appCode, Integer type);

    /**
     * 获取页面默认表单code
     *
     * @param appCode 应用编码
     * @return 页面默认表单code
     */
    String getDefaultFormCode(String appCode);

    /**
     * 查询某个应用下的所有表单
     *
     * @param appCode 应用编码，用于查询对应应用的所有表单
     * @return 查询某个应用下的所有表单
     */
    List<XnSelectDTO> getAllForm(String appCode);

    /**
     * 移动页面
     *
     * @param moveId   移动页面的id
     * @param targetId 基准节点id
     * @param type     移动方向（2：基准节点下级，1：基准节点前方，0：基准节点后方）
     * @return 移动后的页面
     */
    List<ZdPageDTO> move(Long moveId, Long targetId, Integer type);

    /**
     * 通过页面编码获取应用编码
     *
     * @param pageCodeList 页面编码
     * @return 应用编码
     */
    String getAppCodeByCode(List<String> pageCodeList);

    /**
     * 通过页面编码获取应用编码
     *
     * @param pageCode 页面编码
     * @return 应用编码
     */
    String getAppCodeByCode(String pageCode);

    /**
     * 通过页面id获取应用编码
     *
     * @param id 页面id
     * @return 应用编码
     */
    String getAppCodeById(Long id);

    /**
     * 更新页面自定义URL配置
     *
     * @param pageCustomUrl 新的URL配置
     * @param orgId         操作人员组织ID
     * @implNote 需要同时更新URL主表和关联的组织权限信息
     */
    void savePageCustomUrl(ZdPagePublishDTO pageCustomUrl, String orgId);

    /**
     * 通过应用编码获取分组列表
     *
     * @param appCode 应用编码
     * @return 分组列表
     */
    List<ZdOptionDTO<Long>> groupList(String appCode);

    /**
     * 获取应用分组树形结构
     *
     * @param appCode 应用编码
     * @return 按分组组织的页面树形结构
     * @implNote 返回数据包含分组节点和页面节点的层级关系
     */
    List<ZdPageDTO> groupTree(String appCode);

    /**
     * 更新代码字段值
     *
     * @implNote 主要用于维护页面编码字段的版本号更新操作
     */
    void updateCodeVal();


    /**
     * 分页查询广场页面
     *
     * @param paramDTO 查询条件参数
     * @return 分页返回符合条件的页面列表
     * @implNote 支持按名称、分类等多条件组合查询
     */
    IPage<ZdPageSquareDTO> squareSearch(ZdPageSquareSearchParamDTO paramDTO);


}
