package com.sinitek.sirm.nocode.page.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sinitek.data.model.tree.mapper.BaseNodeMapper;
import com.sinitek.sirm.nocode.page.entity.ZdPage;
import com.sinitek.sirm.nocode.page.po.ZdPageSquarePO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 2025-03-12 10:00:09
 * @description 针对表【zd_page(页面表)】的数据库操作Mapper
 * @Entity com.sinitek.sirm.nocode.page.entity.ZdPage
 */
public interface ZdPageMapper extends BaseNodeMapper<ZdPage> {


    /**
     * 页面广场搜索功能的分页查询接口方法
     *
     * @param page  分页参数及结果封装对象，用于指定分页信息并承载查询结果
     * @param param 查询条件参数集合，包含页面名称、分类、标签等筛选条件
     * @return IPage<ZdPageSquarePO> 返回符合查询条件的分页数据对象，封装了查询结果列表及分页信息
     */
    IPage<ZdPageSquarePO> squareSearch(IPage<ZdPageSquarePO> page, @Param("params") Map<String, Object> param);

    /**
     * 查询指定应用的页面树列表接口方法
     *
     * @param appCode       应用编码，用于筛选属于特定应用的页面数据
     * @param hasPublishForm 是否包含已发布的表单，用于过滤具有已发布表单的页面
     * @return List<ZdPage> 返回符合查询条件的页面树数据列表
     */
    List<ZdPage> listTree(@Param("appCode") String appCode,@Param("hasPublishForm")Boolean hasPublishForm);

}




