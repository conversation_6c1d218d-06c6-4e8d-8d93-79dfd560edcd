package com.sinitek.sirm.nocode.page.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sinitek.data.model.tree.entity.BaseNodeEntity;
import com.sinitek.sirm.nocode.page.enumerate.PagePublishTypeEnum;
import com.sinitek.sirm.nocode.page.enumerate.PageTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 页面表
 *
 * @TableName zd_page
 */
@EqualsAndHashCode(callSuper = true)
@TableName(value = "zd_page")
@Data
@ApiModel(description = "页面实体")
public class ZdPage extends BaseNodeEntity {
    /**
     * 名称
     */
    @NotBlank(message = "名称不能为空")
    @Length(max = 100, message = "名称不能超过100个字符")
    @ApiModelProperty(value = "名称", example = "我的简历")
    private String name;

    /**
     * 应用编码
     */
    @NotBlank(message = "应用编码不能为空")
    @ApiModelProperty(value = "应用编码", example = "app_493d587de0304f35af50ab9cd33ece9d")
    private String appCode;

    /**
     * 页面编码
     */
    @ApiModelProperty(value = "页面编码", example = "page_23dcc69621b342f4b46a33473c25f6b9")
    private String code;

    /**
     * 页面自定义地址
     */
    @ApiModelProperty(value = "页面自定义地址", example = "abc1000")
    private String url;

    /**
     * 页面类型
     */
    @NotNull(message = "页面类型不能为空")
    private PageTypeEnum pageType;

    /**
     * 页面发布类型
     */

    private PagePublishTypeEnum publishType;

    /**
     * 跟踪id
     */
    @ApiModelProperty(value = "跟踪id,来源于本表主键", example = "100000")
    private Long threadId;


    @TableField(exist = false)
    @ApiModelProperty(value = "是否有发布表单", example = "true")
    private Boolean formPublishFlag;
}
